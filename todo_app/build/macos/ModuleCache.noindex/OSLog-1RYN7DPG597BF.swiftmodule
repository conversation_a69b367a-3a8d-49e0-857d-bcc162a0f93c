---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/14.5/OSLog.swiftmodule/arm64e-apple-macos.swiftmodule'
dependencies:
  - mtime:           1714073508000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/14.5/OSLog.swiftmodule/arm64e-apple-macos.swiftmodule'
    size:            46336
  - mtime:           1713029605000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            2093130
    sdk_relative:    true
  - mtime:           1713010429000000000
    path:            'usr/include/Darwin.apinotes'
    size:            1133
    sdk_relative:    true
  - mtime:           1713029973000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            25393
    sdk_relative:    true
  - mtime:           1713029890000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            154001
    sdk_relative:    true
  - mtime:           1713029857000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            21924
    sdk_relative:    true
  - mtime:           1713030734000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            169318
    sdk_relative:    true
  - mtime:           1711131215000000000
    path:            'usr/include/objc/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1713016787000000000
    path:            'usr/include/dispatch/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1713030666000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            4531
    sdk_relative:    true
  - mtime:           1713030882000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            56311
    sdk_relative:    true
  - mtime:           1713031032000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22166
    sdk_relative:    true
  - mtime:           1713499277000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1713505594000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1713031044000000000
    path:            'usr/lib/swift/XPC.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            31417
    sdk_relative:    true
  - mtime:           1713031199000000000
    path:            'usr/lib/swift/IOKit.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3475
    sdk_relative:    true
  - mtime:           1713029911000000000
    path:            'usr/lib/swift/Observation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3548
    sdk_relative:    true
  - mtime:           1713498544000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            902072
    sdk_relative:    true
  - mtime:           1713031047000000000
    path:            'usr/lib/swift/os.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            117044
    sdk_relative:    true
  - mtime:           1713032395000000000
    path:            'usr/lib/swift/OSLog.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1122
    sdk_relative:    true
version:         1
...
