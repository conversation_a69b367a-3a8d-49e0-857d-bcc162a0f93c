---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/14.5/SwiftUI.swiftmodule/arm64e-apple-macos.swiftmodule'
dependencies:
  - mtime:           1714073665000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/14.5/SwiftUI.swiftmodule/arm64e-apple-macos.swiftmodule'
    size:            5564064
  - mtime:           1713029605000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            2093130
    sdk_relative:    true
  - mtime:           1713010429000000000
    path:            'usr/include/Darwin.apinotes'
    size:            1133
    sdk_relative:    true
  - mtime:           1711131215000000000
    path:            'usr/include/objc/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1713016787000000000
    path:            'usr/include/dispatch/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1713413498000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            51513
    sdk_relative:    true
  - mtime:           1713499277000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1713505594000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1713029973000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            25393
    sdk_relative:    true
  - mtime:           1713029890000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            154001
    sdk_relative:    true
  - mtime:           1713029857000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            21924
    sdk_relative:    true
  - mtime:           1713030734000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            169318
    sdk_relative:    true
  - mtime:           1713030666000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            4531
    sdk_relative:    true
  - mtime:           1713030882000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            56311
    sdk_relative:    true
  - mtime:           1713031032000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22166
    sdk_relative:    true
  - mtime:           1713031044000000000
    path:            'usr/lib/swift/XPC.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            31417
    sdk_relative:    true
  - mtime:           1713031199000000000
    path:            'usr/lib/swift/IOKit.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3475
    sdk_relative:    true
  - mtime:           1713029911000000000
    path:            'usr/lib/swift/Observation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3548
    sdk_relative:    true
  - mtime:           1713498544000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            902072
    sdk_relative:    true
  - mtime:           1713031372000000000
    path:            'usr/lib/swift/CoreGraphics.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            42993
    sdk_relative:    true
  - mtime:           1713032553000000000
    path:            'System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            15385
    sdk_relative:    true
  - mtime:           1713113972000000000
    path:            'System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes'
    size:            1662
    sdk_relative:    true
  - mtime:           1714014517000000000
    path:            'System/Library/Frameworks/ApplicationServices.framework/Headers/ApplicationServices.apinotes'
    size:            2012
    sdk_relative:    true
  - mtime:           1711133789000000000
    path:            'System/Library/Frameworks/CoreData.framework/Headers/CoreData.apinotes'
    size:            7789
    sdk_relative:    true
  - mtime:           1712708339000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            77061
    sdk_relative:    true
  - mtime:           1713506963000000000
    path:            'System/Library/Frameworks/CoreImage.framework/Headers/CoreImage.apinotes'
    size:            36716
    sdk_relative:    true
  - mtime:           1713498420000000000
    path:            'System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes'
    size:            7428
    sdk_relative:    true
  - mtime:           1714015580000000000
    path:            'System/Library/Frameworks/AppKit.framework/Headers/AppKit.apinotes'
    size:            383637
    sdk_relative:    true
  - mtime:           1713498216000000000
    path:            'System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            42009
    sdk_relative:    true
  - mtime:           1713032331000000000
    path:            'usr/lib/swift/Metal.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            24158
    sdk_relative:    true
  - mtime:           1713032556000000000
    path:            'usr/lib/swift/QuartzCore.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1608
    sdk_relative:    true
  - mtime:           1713032867000000000
    path:            'System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            11884
    sdk_relative:    true
  - mtime:           1713032540000000000
    path:            'usr/lib/swift/CoreImage.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            562
    sdk_relative:    true
  - mtime:           1713420234000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1713419074000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            19216
    sdk_relative:    true
  - mtime:           1713031047000000000
    path:            'usr/lib/swift/os.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            117044
    sdk_relative:    true
  - mtime:           1713035067000000000
    path:            'System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            21032
    sdk_relative:    true
  - mtime:           1713032231000000000
    path:            'System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            8029
    sdk_relative:    true
  - mtime:           1714015732000000000
    path:            'System/Library/Frameworks/AppKit.framework/Modules/AppKit.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            66175
    sdk_relative:    true
  - mtime:           1713032395000000000
    path:            'usr/lib/swift/OSLog.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1122
    sdk_relative:    true
  - mtime:           1713423292000000000
    path:            'System/Library/Frameworks/SwiftUI.framework/Modules/SwiftUI.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1532731
    sdk_relative:    true
version:         1
...
