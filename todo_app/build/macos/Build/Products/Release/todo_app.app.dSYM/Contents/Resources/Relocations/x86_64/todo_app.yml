---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Release/todo_app.app/Contents/MacOS/todo_app'
relocations:
  - { offsetInCU: 0x27, offset: 0xC968A, size: 0x8, addend: 0x0, symName: '_$s8todo_app17MainFlutterWindowC12awakeFromNibyyF', symObjAddr: 0x0, symBinAddr: 0x100001670, symSize: 0x140 }
  - { offsetInCU: 0x8E, offset: 0xC96F1, size: 0x8, addend: 0x0, symName: '_$s8todo_app17MainFlutterWindowC12awakeFromNibyyFTo', symObjAddr: 0x140, symBinAddr: 0x1000017B0, symSize: 0x30 }
  - { offsetInCU: 0xD4, offset: 0xC9737, size: 0x8, addend: 0x0, symName: '_$s8todo_app17MainFlutterWindowC11contentRect9styleMask7backing5deferACSo6CGRectV_So013NSWindowStyleI0VSo18NSBackingStoreTypeVSbtcfcTo', symObjAddr: 0x170, symBinAddr: 0x1000017E0, symSize: 0x80 }
  - { offsetInCU: 0x14D, offset: 0xC97B0, size: 0x8, addend: 0x0, symName: '_$s8todo_app17MainFlutterWindowCMa', symObjAddr: 0x220, symBinAddr: 0x100001890, symSize: 0x14 }
  - { offsetInCU: 0x358, offset: 0xC99BB, size: 0x8, addend: 0x0, symName: '_$s8todo_app17MainFlutterWindowC12awakeFromNibyyF', symObjAddr: 0x0, symBinAddr: 0x100001670, symSize: 0x140 }
  - { offsetInCU: 0x47D, offset: 0xC9AE0, size: 0x8, addend: 0x0, symName: '_$s8todo_app17MainFlutterWindowCfD', symObjAddr: 0x1F0, symBinAddr: 0x100001860, symSize: 0x30 }
  - { offsetInCU: 0x27, offset: 0xC9B2F, size: 0x8, addend: 0x0, symName: '_$s8todo_app11AppDelegateC47applicationShouldTerminateAfterLastWindowClosedySbSo13NSApplicationCFTo', symObjAddr: 0x0, symBinAddr: 0x1000018B0, symSize: 0x10 }
  - { offsetInCU: 0x4B, offset: 0xC9B53, size: 0x8, addend: 0x0, symName: '_$s8todo_app11AppDelegateC47applicationShouldTerminateAfterLastWindowClosedySbSo13NSApplicationCFTo', symObjAddr: 0x0, symBinAddr: 0x1000018B0, symSize: 0x10 }
  - { offsetInCU: 0x67, offset: 0xC9B6F, size: 0x8, addend: 0x0, symName: '_$s8todo_app11AppDelegateC40applicationSupportsSecureRestorableStateySbSo13NSApplicationCFTo', symObjAddr: 0x10, symBinAddr: 0x1000018C0, symSize: 0x10 }
  - { offsetInCU: 0x99, offset: 0xC9BA1, size: 0x8, addend: 0x0, symName: '_$s8todo_app11AppDelegateCACycfcTo', symObjAddr: 0x20, symBinAddr: 0x1000018D0, symSize: 0x30 }
  - { offsetInCU: 0x100, offset: 0xC9C08, size: 0x8, addend: 0x0, symName: _main, symObjAddr: 0x80, symBinAddr: 0x100001930, symSize: 0x30 }
  - { offsetInCU: 0x14A, offset: 0xC9C52, size: 0x8, addend: 0x0, symName: '_$s8todo_app11AppDelegateCMa', symObjAddr: 0xB0, symBinAddr: 0x100001960, symSize: 0x14 }
  - { offsetInCU: 0x2A5, offset: 0xC9DAD, size: 0x8, addend: 0x0, symName: '_$s8todo_app11AppDelegateCfD', symObjAddr: 0x50, symBinAddr: 0x100001900, symSize: 0x30 }
  - { offsetInCU: 0x2B, offset: 0xC9E1E, size: 0x8, addend: 0x0, symName: '_$s8todo_app19ResourceBundleClass33_7BF7EA91367A9BAC188D0D83AC6BD852LLCfD', symObjAddr: 0x0, symBinAddr: 0x100001980, symSize: 0x20 }
  - { offsetInCU: 0x6D, offset: 0xC9E60, size: 0x8, addend: 0x0, symName: '_$s8todo_app19ResourceBundleClass33_7BF7EA91367A9BAC188D0D83AC6BD852LLCMa', symObjAddr: 0x20, symBinAddr: 0x1000019A0, symSize: 0x14 }
  - { offsetInCU: 0x183, offset: 0xC9F76, size: 0x8, addend: 0x0, symName: '_$s8todo_app19ResourceBundleClass33_7BF7EA91367A9BAC188D0D83AC6BD852LLCfD', symObjAddr: 0x0, symBinAddr: 0x100001980, symSize: 0x20 }
...
