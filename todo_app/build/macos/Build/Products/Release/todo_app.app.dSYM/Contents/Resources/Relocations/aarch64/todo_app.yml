---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Release/todo_app.app/Contents/MacOS/todo_app'
relocations:
  - { offsetInCU: 0x27, offset: 0xC6548, size: 0x8, addend: 0x0, symName: '_$s8todo_app17MainFlutterWindowC12awakeFromNibyyF', symObjAddr: 0x0, symBinAddr: 0x10000267C, symSize: 0x13C }
  - { offsetInCU: 0x8E, offset: 0xC65AF, size: 0x8, addend: 0x0, symName: '_$s8todo_app17MainFlutterWindowC12awakeFromNibyyFTo', symObjAddr: 0x13C, symBinAddr: 0x1000027B8, symSize: 0x28 }
  - { offsetInCU: 0xD4, offset: 0xC65F5, size: 0x8, addend: 0x0, symName: '_$s8todo_app17MainFlutterWindowC11contentRect9styleMask7backing5deferACSo6CGRectV_So013NSWindowStyleI0VSo18NSBackingStoreTypeVSbtcfcTo', symObjAddr: 0x164, symBinAddr: 0x1000027E0, symSize: 0x8C }
  - { offsetInCU: 0x136, offset: 0xC6657, size: 0x8, addend: 0x0, symName: '_$s8todo_app17MainFlutterWindowCMa', symObjAddr: 0x220, symBinAddr: 0x10000289C, symSize: 0x20 }
  - { offsetInCU: 0x346, offset: 0xC6867, size: 0x8, addend: 0x0, symName: '_$s8todo_app17MainFlutterWindowC12awakeFromNibyyF', symObjAddr: 0x0, symBinAddr: 0x10000267C, symSize: 0x13C }
  - { offsetInCU: 0x460, offset: 0xC6981, size: 0x8, addend: 0x0, symName: '_$s8todo_app17MainFlutterWindowCfD', symObjAddr: 0x1F0, symBinAddr: 0x10000286C, symSize: 0x30 }
  - { offsetInCU: 0x27, offset: 0xC69D0, size: 0x8, addend: 0x0, symName: '_$s8todo_app11AppDelegateC47applicationShouldTerminateAfterLastWindowClosedySbSo13NSApplicationCFTo', symObjAddr: 0x0, symBinAddr: 0x1000028BC, symSize: 0x8 }
  - { offsetInCU: 0x4B, offset: 0xC69F4, size: 0x8, addend: 0x0, symName: '_$s8todo_app11AppDelegateC47applicationShouldTerminateAfterLastWindowClosedySbSo13NSApplicationCFTo', symObjAddr: 0x0, symBinAddr: 0x1000028BC, symSize: 0x8 }
  - { offsetInCU: 0x67, offset: 0xC6A10, size: 0x8, addend: 0x0, symName: '_$s8todo_app11AppDelegateC40applicationSupportsSecureRestorableStateySbSo13NSApplicationCFTo', symObjAddr: 0x8, symBinAddr: 0x1000028C4, symSize: 0x8 }
  - { offsetInCU: 0x99, offset: 0xC6A42, size: 0x8, addend: 0x0, symName: '_$s8todo_app11AppDelegateCACycfcTo', symObjAddr: 0x10, symBinAddr: 0x1000028CC, symSize: 0x3C }
  - { offsetInCU: 0xFE, offset: 0xC6AA7, size: 0x8, addend: 0x0, symName: _main, symObjAddr: 0x7C, symBinAddr: 0x100002938, symSize: 0x28 }
  - { offsetInCU: 0x148, offset: 0xC6AF1, size: 0x8, addend: 0x0, symName: '_$s8todo_app11AppDelegateCMa', symObjAddr: 0xA4, symBinAddr: 0x100002960, symSize: 0x20 }
  - { offsetInCU: 0x2A3, offset: 0xC6C4C, size: 0x8, addend: 0x0, symName: '_$s8todo_app11AppDelegateCfD', symObjAddr: 0x4C, symBinAddr: 0x100002908, symSize: 0x30 }
  - { offsetInCU: 0x2B, offset: 0xC6CBD, size: 0x8, addend: 0x0, symName: '_$s8todo_app19ResourceBundleClass33_7BF7EA91367A9BAC188D0D83AC6BD852LLCfD', symObjAddr: 0x0, symBinAddr: 0x100002980, symSize: 0x10 }
  - { offsetInCU: 0x6D, offset: 0xC6CFF, size: 0x8, addend: 0x0, symName: '_$s8todo_app19ResourceBundleClass33_7BF7EA91367A9BAC188D0D83AC6BD852LLCMa', symObjAddr: 0x10, symBinAddr: 0x100002990, symSize: 0x20 }
  - { offsetInCU: 0x183, offset: 0xC6E15, size: 0x8, addend: 0x0, symName: '_$s8todo_app19ResourceBundleClass33_7BF7EA91367A9BAC188D0D83AC6BD852LLCfD', symObjAddr: 0x0, symBinAddr: 0x100002980, symSize: 0x10 }
...
