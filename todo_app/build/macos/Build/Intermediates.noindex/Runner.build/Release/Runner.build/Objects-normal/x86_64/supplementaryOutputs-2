"/Users/<USER>/Desktop/dart-test/todo_app/macos/Runner/MainFlutterWindow.swift":
  abi-baseline-json: "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/todo_app.abi.json"
  const-values: "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/Runner-master.swiftconstvalues"
  objc-header: "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/todo_app-Swift.h"
  diagnostics: "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/Runner-master.dia"
  swiftmodule: "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/todo_app.swiftmodule"
  swiftdoc: "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/todo_app.swiftdoc"
  dependencies: "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/Runner-master.d"
  swiftsourceinfo: "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/todo_app.swiftsourceinfo"
