{"": {"const-values": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/Objects-normal/x86_64/shared_preferences_foundation-master.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/Objects-normal/x86_64/shared_preferences_foundation-master.d", "diagnostics": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/Objects-normal/x86_64/shared_preferences_foundation-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/Objects-normal/x86_64/shared_preferences_foundation-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/Objects-normal/x86_64/shared_preferences_foundation-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/Objects-normal/x86_64/shared_preferences_foundation-master.swiftdeps"}, "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift": {"index-unit-output-path": "/Pods.build/Release/shared_preferences_foundation.build/Objects-normal/x86_64/messages.g.o", "llvm-bc": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/Objects-normal/x86_64/messages.g.bc", "object": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/Objects-normal/x86_64/messages.g.o"}, "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift": {"index-unit-output-path": "/Pods.build/Release/shared_preferences_foundation.build/Objects-normal/x86_64/SharedPreferencesPlugin.o", "llvm-bc": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/Objects-normal/x86_64/SharedPreferencesPlugin.bc", "object": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/Objects-normal/x86_64/SharedPreferencesPlugin.o"}}