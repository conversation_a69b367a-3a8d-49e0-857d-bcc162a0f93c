-target arm64-apple-macos10.14 '-std=gnu11' -fobjc-arc -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Desktop/dart-test/todo_app/build/macos/ModuleCache.noindex' '-fmodule-name=shared_preferences_foundation' -fpascal-strings -Os -fno-common '-DPOD_CONFIGURATION_RELEASE=1' '-DCOCOAPODS=1' '-DNS_BLOCK_ASSERTIONS=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk -g -iquote /Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/shared_preferences_foundation-generated-files.hmap -I/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/shared_preferences_foundation-own-target-headers.hmap -I/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/shared_preferences_foundation-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/all-product-headers.yaml -iquote /Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/shared_preferences_foundation-project-headers.hmap -I/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Release/shared_preferences_foundation/include -I/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/DerivedSources-normal/arm64 -I/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/DerivedSources/arm64 -I/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/DerivedSources -F/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Release/shared_preferences_foundation -F/usr/local/share/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64