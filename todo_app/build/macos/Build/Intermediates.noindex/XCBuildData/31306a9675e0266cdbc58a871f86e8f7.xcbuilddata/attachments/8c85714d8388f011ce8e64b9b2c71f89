-target x86_64-apple-macos10.14 '-std=gnu11' -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Desktop/dart-test/todo_app/build/macos/ModuleCache.noindex' '-fmodule-name=Pods_Runner' -fpascal-strings -Os -fno-common '-DPOD_CONFIGURATION_RELEASE=1' '-DCOCOAPODS=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk -fasm-blocks -g -iquote /Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/Pods_Runner-generated-files.hmap -I/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/Pods_Runner-own-target-headers.hmap -I/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/Pods_Runner-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/all-product-headers.yaml -iquote /Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/Pods_Runner-project-headers.hmap -I/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Release/include -I/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Release/shared_preferences_foundation/shared_preferences_foundation.framework/Headers -I/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/DerivedSources-normal/x86_64 -I/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/DerivedSources/x86_64 -I/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/DerivedSources -F/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Release -F/usr/local/share/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64 -F/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Release/shared_preferences_foundation