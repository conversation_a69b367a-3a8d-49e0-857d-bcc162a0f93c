{"case-sensitive": "false", "roots": [{"contents": [{"external-contents": "/Users/<USER>/Desktop/dart-test/todo_app/macos/Pods/Target Support Files/Pods-Runner/Pods-Runner-umbrella.h", "name": "Pods-Runner-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Debug/Pods_Runner.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-Runner.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Debug/Pods_Runner.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/dart-test/todo_app/macos/Pods/Target Support Files/shared_preferences_foundation/shared_preferences_foundation-umbrella.h", "name": "shared_preferences_foundation-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Headers/shared_preferences_foundation-Swift.h", "name": "shared_preferences_foundation-Swift.h", "type": "file"}], "name": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Versions/A/Headers", "type": "directory"}], "version": 0}