{"": {"diagnostics": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/shared_preferences_foundation-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/shared_preferences_foundation-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/shared_preferences_foundation-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/shared_preferences_foundation-master.swiftdeps"}, "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift": {"const-values": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/messages.g.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/messages.g.d", "diagnostics": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/messages.g.dia", "index-unit-output-path": "/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/messages.g.o", "llvm-bc": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/messages.g.bc", "object": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/messages.g.o", "swift-dependencies": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/messages.g.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/messages.g~partial.swiftmodule"}, "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift": {"const-values": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/SharedPreferencesPlugin.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/SharedPreferencesPlugin.d", "diagnostics": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/SharedPreferencesPlugin.dia", "index-unit-output-path": "/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/SharedPreferencesPlugin.o", "llvm-bc": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/SharedPreferencesPlugin.bc", "object": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/SharedPreferencesPlugin.o", "swift-dependencies": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/SharedPreferencesPlugin.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/dart-test/todo_app/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/Objects-normal/x86_64/SharedPreferencesPlugin~partial.swiftmodule"}}