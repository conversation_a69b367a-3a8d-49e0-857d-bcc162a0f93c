import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

// 待办事项数据模型
class TodoItem {
  String id;
  String title;
  bool isCompleted;
  DateTime createdAt;
  DateTime? dueDate; // 截止日期
  String category; // 分类
  List<String> tags; // 标签

  TodoItem({
    required this.id,
    required this.title,
    this.isCompleted = false,
    DateTime? createdAt,
    this.dueDate,
    this.category = '默认',
    this.tags = const [],
  }) : createdAt = createdAt ?? DateTime.now();

  // 检查是否过期
  bool get isOverdue {
    if (dueDate == null || isCompleted) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  // 检查是否即将到期（24小时内）
  bool get isDueSoon {
    if (dueDate == null || isCompleted) return false;
    final now = DateTime.now();
    final tomorrow = now.add(const Duration(days: 1));
    return dueDate!.isAfter(now) && dueDate!.isBefore(tomorrow);
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'isCompleted': isCompleted,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'dueDate': dueDate?.millisecondsSinceEpoch,
      'category': category,
      'tags': tags,
    };
  }

  // 从JSON创建TodoItem
  factory TodoItem.fromJson(Map<String, dynamic> json) {
    return TodoItem(
      id: json['id'],
      title: json['title'],
      isCompleted: json['isCompleted'] ?? false,
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      dueDate: json['dueDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['dueDate'])
          : null,
      category: json['category'] ?? '默认',
      tags: List<String>.from(json['tags'] ?? []),
    );
  }
}

void main() {
  runApp(const TodoApp());
}

class TodoApp extends StatefulWidget {
  const TodoApp({super.key});

  @override
  State<TodoApp> createState() => _TodoAppState();
}

class _TodoAppState extends State<TodoApp> {
  bool _isDarkMode = false;
  static const String _themeKey = 'isDarkMode';

  @override
  void initState() {
    super.initState();
    _loadThemePreference();
  }

  // 加载主题偏好
  Future<void> _loadThemePreference() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool(_themeKey) ?? false;
    });
  }

  // 保存主题偏好
  Future<void> _saveThemePreference(bool isDark) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_themeKey, isDark);
  }

  // 切换主题
  void _toggleTheme() {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
    _saveThemePreference(_isDarkMode);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '待办事项管理器',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(
          centerTitle: true,
          elevation: 2,
        ),
        cardTheme: const CardThemeData(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
        ),
      ),
      darkTheme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(
          centerTitle: true,
          elevation: 2,
        ),
        cardTheme: const CardThemeData(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
        ),
        // 深色模式下的特殊配置
        scaffoldBackgroundColor: const Color(0xFF121212),
        dialogBackgroundColor: const Color(0xFF1E1E1E),
      ),
      themeMode: _isDarkMode ? ThemeMode.dark : ThemeMode.light,
      home: TodoHomePage(onThemeToggle: _toggleTheme, isDarkMode: _isDarkMode),
    );
  }
}

class TodoHomePage extends StatefulWidget {
  final VoidCallback onThemeToggle;
  final bool isDarkMode;

  const TodoHomePage({
    super.key,
    required this.onThemeToggle,
    required this.isDarkMode,
  });

  @override
  State<TodoHomePage> createState() => _TodoHomePageState();
}

class _TodoHomePageState extends State<TodoHomePage> {
  int _currentIndex = 0;
  final List<TodoItem> _todos = [];
  final TextEditingController _searchController = TextEditingController();
  static const String _todosKey = 'todos';
  
  final List<String> _categories = ['默认', '工作', '学习', '生活', '健康'];
  String _searchQuery = '';
  String _filterCategory = '全部';

  @override
  void initState() {
    super.initState();
    _loadTodos();
  }

  // 加载保存的待办事项
  Future<void> _loadTodos() async {
    final prefs = await SharedPreferences.getInstance();
    final todosJson = prefs.getString(_todosKey);
    if (todosJson != null) {
      final List<dynamic> todosList = json.decode(todosJson);
      setState(() {
        _todos.clear();
        _todos.addAll(todosList.map((json) => TodoItem.fromJson(json)));
      });
    }
  }

  // 保存待办事项到本地
  Future<void> _saveTodos() async {
    final prefs = await SharedPreferences.getInstance();
    final todosJson = json.encode(_todos.map((todo) => todo.toJson()).toList());
    await prefs.setString(_todosKey, todosJson);
  }

  // 获取过滤后的待办事项
  List<TodoItem> get _filteredTodos {
    List<TodoItem> filtered = _todos;

    // 按搜索关键词过滤
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((todo) =>
          todo.title.toLowerCase().contains(_searchQuery.toLowerCase())).toList();
    }

    // 按分类过滤
    if (_filterCategory != '全部') {
      filtered = filtered.where((todo) => todo.category == _filterCategory).toList();
    }

    // 按截止日期排序
    filtered.sort((a, b) {
      // 过期的排在前面
      if (a.isOverdue && !b.isOverdue) return -1;
      if (!a.isOverdue && b.isOverdue) return 1;
      
      // 即将到期的排在前面
      if (a.isDueSoon && !b.isDueSoon) return -1;
      if (!a.isDueSoon && b.isDueSoon) return 1;
      
      // 按截止日期排序
      if (a.dueDate != null && b.dueDate != null) {
        return a.dueDate!.compareTo(b.dueDate!);
      }
      if (a.dueDate != null) return -1;
      if (b.dueDate != null) return 1;
      
      // 最后按创建时间排序
      return b.createdAt.compareTo(a.createdAt);
    });

    return filtered;
  }

  // 显示添加任务对话框
  void _showAddTodoDialog() {
    showDialog(
      context: context,
      builder: (context) => AddTodoDialog(
        categories: _categories,
        onAdd: (title, dueDate, category) {
          setState(() {
            _todos.add(TodoItem(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              title: title,
              dueDate: dueDate,
              category: category,
            ));
          });
          _saveTodos();
        },
      ),
    );
  }

  // 切换待办事项完成状态
  void _toggleTodo(String id) {
    setState(() {
      final index = _todos.indexWhere((todo) => todo.id == id);
      if (index != -1) {
        _todos[index].isCompleted = !_todos[index].isCompleted;
      }
    });
    _saveTodos();
  }

  // 删除待办事项
  void _deleteTodo(String id) {
    setState(() {
      _todos.removeWhere((todo) => todo.id == id);
    });
    _saveTodos();
  }

  // 格式化日期显示
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // 获取分类颜色
  Color _getCategoryColor(String category) {
    switch (category) {
      case '工作':
        return Colors.blue;
      case '学习':
        return Colors.green;
      case '生活':
        return Colors.purple;
      case '健康':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: [
          _buildTodoListPage(),
          _buildStatisticsPage(),
          _buildSettingsPage(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.list),
            label: '任务列表',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: '统计',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: '设置',
          ),
        ],
      ),
      floatingActionButton: _currentIndex == 0
          ? FloatingActionButton(
              onPressed: _showAddTodoDialog,
              tooltip: '添加待办事项',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  // 构建任务列表页面
  Widget _buildTodoListPage() {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('待办事项管理器'),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // 搜索和过滤区域
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // 搜索栏
                TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: '搜索待办事项...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                // 分类过滤
                Row(
                  children: [
                    const Text('分类: '),
                    Expanded(
                      child: DropdownButton<String>(
                        value: _filterCategory,
                        isExpanded: true,
                        items: ['全部', ..._categories].map((category) {
                          return DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _filterCategory = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 待办事项列表
          Expanded(
            child: _filteredTodos.isEmpty
                ? const Center(
                    child: Text(
                      '没有找到匹配的待办事项',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : ListView.builder(
                    itemCount: _filteredTodos.length,
                    itemBuilder: (context, index) {
                      final todo = _filteredTodos[index];
                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 4,
                        ),
                        color: todo.isOverdue
                            ? Colors.red.shade50
                            : todo.isDueSoon
                                ? Colors.orange.shade50
                                : null,
                        child: ListTile(
                          leading: Checkbox(
                            value: todo.isCompleted,
                            onChanged: (_) => _toggleTodo(todo.id),
                          ),
                          title: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  todo.title,
                                  style: TextStyle(
                                    decoration: todo.isCompleted
                                        ? TextDecoration.lineThrough
                                        : null,
                                    color: todo.isCompleted
                                        ? Colors.grey
                                        : null,
                                  ),
                                ),
                              ),
                              // 分类标签
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: _getCategoryColor(todo.category),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  todo.category,
                                  style: const TextStyle(
                                    fontSize: 10,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (todo.dueDate != null)
                                Row(
                                  children: [
                                    Icon(
                                      Icons.schedule,
                                      size: 14,
                                      color: todo.isOverdue
                                          ? Colors.red
                                          : todo.isDueSoon
                                              ? Colors.orange
                                              : Colors.grey,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      '截止: ${_formatDate(todo.dueDate!)}',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: todo.isOverdue
                                            ? Colors.red
                                            : todo.isDueSoon
                                                ? Colors.orange
                                                : Colors.grey,
                                      ),
                                    ),
                                    if (todo.isOverdue)
                                      const Text(
                                        ' (已过期)',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.red,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    if (todo.isDueSoon && !todo.isOverdue)
                                      const Text(
                                        ' (即将到期)',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.orange,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                  ],
                                ),
                              Text(
                                '创建于: ${_formatDate(todo.createdAt)}',
                                style: const TextStyle(fontSize: 10, color: Colors.grey),
                              ),
                            ],
                          ),
                          trailing: IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () => _deleteTodo(todo.id),
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  // 构建统计页面
  Widget _buildStatisticsPage() {
    final totalTasks = _todos.length;
    final completedTasks = _todos.where((todo) => todo.isCompleted).length;
    final overdueTasks = _todos.where((todo) => todo.isOverdue).length;
    final dueSoonTasks = _todos.where((todo) => todo.isDueSoon).length;
    final completionRate = totalTasks > 0 ? (completedTasks / totalTasks) : 0.0;

    // 按分类统计
    final Map<String, int> categoryStats = {};
    final Map<String, int> categoryCompleted = {};
    for (final todo in _todos) {
      categoryStats[todo.category] = (categoryStats[todo.category] ?? 0) + 1;
      if (todo.isCompleted) {
        categoryCompleted[todo.category] = (categoryCompleted[todo.category] ?? 0) + 1;
      }
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('统计信息'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 总体统计卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '总体统计',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            '总任务',
                            totalTasks.toString(),
                            Icons.list_alt,
                            Colors.blue,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatCard(
                            '已完成',
                            completedTasks.toString(),
                            Icons.check_circle,
                            Colors.green,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            '已过期',
                            overdueTasks.toString(),
                            Icons.warning,
                            Colors.red,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatCard(
                            '即将到期',
                            dueSoonTasks.toString(),
                            Icons.schedule,
                            Colors.orange,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // 完成率进度条
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '完成率: ${(completionRate * 100).toStringAsFixed(1)}%',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value: completionRate,
                          backgroundColor: Colors.grey.shade300,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            completionRate > 0.8 ? Colors.green :
                            completionRate > 0.5 ? Colors.orange : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // 分类统计
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '分类统计',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...categoryStats.entries.map((entry) {
                      final category = entry.key;
                      final total = entry.value;
                      final completed = categoryCompleted[category] ?? 0;
                      final rate = total > 0 ? (completed / total) : 0.0;

                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      width: 12,
                                      height: 12,
                                      decoration: BoxDecoration(
                                        color: _getCategoryColor(category),
                                        shape: BoxShape.circle,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(category),
                                  ],
                                ),
                                Text('$completed/$total'),
                              ],
                            ),
                            const SizedBox(height: 4),
                            LinearProgressIndicator(
                              value: rate,
                              backgroundColor: Colors.grey.shade300,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _getCategoryColor(category),
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建统计卡片
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  // 构建设置页面
  Widget _buildSettingsPage() {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('设置'),
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.dark_mode),
                  title: const Text('深色模式'),
                  subtitle: const Text('切换应用主题'),
                  trailing: Switch(
                    value: widget.isDarkMode,
                    onChanged: (value) {
                      widget.onThemeToggle();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(value ? '已切换到深色模式' : '已切换到浅色模式'),
                          duration: const Duration(seconds: 1),
                        ),
                      );
                    },
                  ),
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.delete_sweep),
                  title: const Text('清除所有数据'),
                  subtitle: const Text('删除所有待办事项'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: _showClearDataDialog,
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.info),
                  title: const Text('关于应用'),
                  subtitle: const Text('版本信息和开发者'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: _showAboutDialog,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '应用统计',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text('总任务数: ${_todos.length}'),
                  Text('已完成: ${_todos.where((t) => t.isCompleted).length}'),
                  Text('使用天数: ${_todos.isEmpty ? 0 : DateTime.now().difference(_todos.map((t) => t.createdAt).reduce((a, b) => a.isBefore(b) ? a : b)).inDays + 1}'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 显示清除数据对话框
  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除所有数据'),
        content: const Text('确定要删除所有待办事项吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _todos.clear();
              });
              _saveTodos();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('所有数据已清除')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('确定删除', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  // 显示关于对话框
  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('关于应用'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('待办事项管理器'),
            Text('版本: 2.0.0'),
            SizedBox(height: 8),
            Text('功能特性:'),
            Text('• 任务管理和分类'),
            Text('• 截止日期提醒'),
            Text('• 搜索和过滤'),
            Text('• 统计和进度跟踪'),
            Text('• 深色模式支持'),
            SizedBox(height: 8),
            Text('使用 Flutter 和 Dart 开发'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

// 添加待办事项对话框
class AddTodoDialog extends StatefulWidget {
  final List<String> categories;
  final Function(String title, DateTime? dueDate, String category) onAdd;

  const AddTodoDialog({
    super.key,
    required this.categories,
    required this.onAdd,
  });

  @override
  State<AddTodoDialog> createState() => _AddTodoDialogState();
}

class _AddTodoDialogState extends State<AddTodoDialog> {
  final TextEditingController _titleController = TextEditingController();
  DateTime? _selectedDueDate;
  String _selectedCategory = '默认';

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('添加待办事项'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题输入
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: '任务标题',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          const SizedBox(height: 16),
          // 分类选择
          DropdownButtonFormField<String>(
            value: _selectedCategory,
            decoration: const InputDecoration(
              labelText: '分类',
              border: OutlineInputBorder(),
            ),
            items: widget.categories.map((category) {
              return DropdownMenuItem(
                value: category,
                child: Text(category),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedCategory = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          // 截止日期选择
          Row(
            children: [
              Expanded(
                child: Text(
                  _selectedDueDate == null
                      ? '未设置截止日期'
                      : '截止日期: ${_formatDate(_selectedDueDate!)}',
                ),
              ),
              TextButton(
                onPressed: _selectDueDate,
                child: const Text('选择日期'),
              ),
              if (_selectedDueDate != null)
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedDueDate = null;
                    });
                  },
                  child: const Text('清除'),
                ),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_titleController.text.trim().isNotEmpty) {
              widget.onAdd(
                _titleController.text.trim(),
                _selectedDueDate,
                _selectedCategory,
              );
              Navigator.of(context).pop();
            }
          },
          child: const Text('添加'),
        ),
      ],
    );
  }

  Future<void> _selectDueDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDueDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        _selectedDueDate = picked;
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }
}
